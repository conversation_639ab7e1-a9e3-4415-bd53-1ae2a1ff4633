<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add slug field for SEO-friendly URLs (nullable initially)
            if (!Schema::hasColumn('users', 'slug')) {
                $table->string('slug')->nullable()->unique()->after('name');
            }

            // Add public_id field for external references (ULID) (nullable initially)
            if (!Schema::hasColumn('users', 'public_id')) {
                $table->string('public_id', 26)->nullable()->unique()->after('id');
            }

            // Add soft deletes support
            if (!Schema::hasColumn('users', 'deleted_at')) {
                $table->softDeletes();
            }

            // Add userstamps fields for audit trail
            if (!Schema::hasColumn('users', 'created_by')) {
                $table->unsignedBigInteger('created_by')->nullable()->after('created_at');
            }
            if (!Schema::hasColumn('users', 'updated_by')) {
                $table->unsignedBigInteger('updated_by')->nullable()->after('updated_at');
            }
            if (!Schema::hasColumn('users', 'deleted_by')) {
                $table->unsignedBigInteger('deleted_by')->nullable()->after('deleted_at');
            }
        });

        // Add indexes and foreign keys in a separate schema call to avoid conflicts
        Schema::table('users', function (Blueprint $table) {
            // Add indexes for performance (with try-catch to handle existing indexes)
            try {
                $table->index('slug');
            } catch (Exception $e) {
                // Index already exists, ignore
            }

            try {
                $table->index('public_id');
            } catch (Exception $e) {
                // Index already exists, ignore
            }

            try {
                $table->index('created_by');
            } catch (Exception $e) {
                // Index already exists, ignore
            }

            try {
                $table->index('updated_by');
            } catch (Exception $e) {
                // Index already exists, ignore
            }

            try {
                $table->index('deleted_by');
            } catch (Exception $e) {
                // Index already exists, ignore
            }

            // Add foreign key constraints for userstamps (with try-catch to handle existing constraints)
            try {
                $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            } catch (Exception $e) {
                // Foreign key already exists, ignore
            }

            try {
                $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
            } catch (Exception $e) {
                // Foreign key already exists, ignore
            }

            try {
                $table->foreign('deleted_by')->references('id')->on('users')->onDelete('set null');
            } catch (Exception $e) {
                // Foreign key already exists, ignore
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop foreign key constraints first (with try-catch to handle non-existing constraints)
            try {
                $table->dropForeign(['created_by']);
            } catch (Exception $e) {
                // Foreign key doesn't exist, ignore
            }

            try {
                $table->dropForeign(['updated_by']);
            } catch (Exception $e) {
                // Foreign key doesn't exist, ignore
            }

            try {
                $table->dropForeign(['deleted_by']);
            } catch (Exception $e) {
                // Foreign key doesn't exist, ignore
            }

            // Drop indexes (with try-catch to handle non-existing indexes)
            try {
                $table->dropIndex(['slug']);
            } catch (Exception $e) {
                // Index doesn't exist, ignore
            }

            try {
                $table->dropIndex(['public_id']);
            } catch (Exception $e) {
                // Index doesn't exist, ignore
            }

            try {
                $table->dropIndex(['created_by']);
            } catch (Exception $e) {
                // Index doesn't exist, ignore
            }

            try {
                $table->dropIndex(['updated_by']);
            } catch (Exception $e) {
                // Index doesn't exist, ignore
            }

            try {
                $table->dropIndex(['deleted_by']);
            } catch (Exception $e) {
                // Index doesn't exist, ignore
            }

            // Drop columns (check if they exist)
            if (Schema::hasColumn('users', 'slug')) {
                $table->dropColumn('slug');
            }
            if (Schema::hasColumn('users', 'public_id')) {
                $table->dropColumn('public_id');
            }
            if (Schema::hasColumn('users', 'deleted_at')) {
                $table->dropColumn('deleted_at');
            }
            if (Schema::hasColumn('users', 'created_by')) {
                $table->dropColumn('created_by');
            }
            if (Schema::hasColumn('users', 'updated_by')) {
                $table->dropColumn('updated_by');
            }
            if (Schema::hasColumn('users', 'deleted_by')) {
                $table->dropColumn('deleted_by');
            }
        });
    }
};
