<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add slug field for SEO-friendly URLs (nullable initially)
            if (!Schema::hasColumn('users', 'slug')) {
                $table->string('slug')->nullable()->unique()->after('name');
            }

            // Add public_id field for external references (ULID) (nullable initially)
            if (!Schema::hasColumn('users', 'public_id')) {
                $table->string('public_id', 26)->nullable()->unique()->after('id');
            }

            // Add soft deletes support
            if (!Schema::hasColumn('users', 'deleted_at')) {
                $table->softDeletes();
            }

            // Add userstamps fields for audit trail
            if (!Schema::hasColumn('users', 'created_by')) {
                $table->unsignedBigInteger('created_by')->nullable()->after('created_at');
            }
            if (!Schema::hasColumn('users', 'updated_by')) {
                $table->unsignedBigInteger('updated_by')->nullable()->after('updated_at');
            }
            if (!Schema::hasColumn('users', 'deleted_by')) {
                $table->unsignedBigInteger('deleted_by')->nullable()->after('deleted_at');
            }
        });

        // Add indexes and foreign keys in a separate schema call to avoid conflicts
        Schema::table('users', function (Blueprint $table) {
            // Add indexes for performance (check if they don't exist)
            $sm = Schema::getConnection()->getDoctrineSchemaManager();
            $indexesNames = array_keys($sm->listTableIndexes('users'));

            if (!in_array('users_slug_index', $indexesNames)) {
                $table->index('slug');
            }
            if (!in_array('users_public_id_index', $indexesNames)) {
                $table->index('public_id');
            }
            if (!in_array('users_created_by_index', $indexesNames)) {
                $table->index('created_by');
            }
            if (!in_array('users_updated_by_index', $indexesNames)) {
                $table->index('updated_by');
            }
            if (!in_array('users_deleted_by_index', $indexesNames)) {
                $table->index('deleted_by');
            }

            // Add foreign key constraints for userstamps (check if they don't exist)
            $foreignKeys = array_keys($sm->listTableForeignKeys('users'));

            if (!in_array('users_created_by_foreign', $foreignKeys)) {
                $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            }
            if (!in_array('users_updated_by_foreign', $foreignKeys)) {
                $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
            }
            if (!in_array('users_deleted_by_foreign', $foreignKeys)) {
                $table->foreign('deleted_by')->references('id')->on('users')->onDelete('set null');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop foreign key constraints first (check if they exist)
            $sm = Schema::getConnection()->getDoctrineSchemaManager();
            $foreignKeys = array_keys($sm->listTableForeignKeys('users'));

            if (in_array('users_created_by_foreign', $foreignKeys)) {
                $table->dropForeign(['created_by']);
            }
            if (in_array('users_updated_by_foreign', $foreignKeys)) {
                $table->dropForeign(['updated_by']);
            }
            if (in_array('users_deleted_by_foreign', $foreignKeys)) {
                $table->dropForeign(['deleted_by']);
            }

            // Drop indexes (check if they exist)
            $indexesNames = array_keys($sm->listTableIndexes('users'));

            if (in_array('users_slug_index', $indexesNames)) {
                $table->dropIndex(['slug']);
            }
            if (in_array('users_public_id_index', $indexesNames)) {
                $table->dropIndex(['public_id']);
            }
            if (in_array('users_created_by_index', $indexesNames)) {
                $table->dropIndex(['created_by']);
            }
            if (in_array('users_updated_by_index', $indexesNames)) {
                $table->dropIndex(['updated_by']);
            }
            if (in_array('users_deleted_by_index', $indexesNames)) {
                $table->dropIndex(['deleted_by']);
            }

            // Drop columns (check if they exist)
            if (Schema::hasColumn('users', 'slug')) {
                $table->dropColumn('slug');
            }
            if (Schema::hasColumn('users', 'public_id')) {
                $table->dropColumn('public_id');
            }
            if (Schema::hasColumn('users', 'deleted_at')) {
                $table->dropColumn('deleted_at');
            }
            if (Schema::hasColumn('users', 'created_by')) {
                $table->dropColumn('created_by');
            }
            if (Schema::hasColumn('users', 'updated_by')) {
                $table->dropColumn('updated_by');
            }
            if (Schema::hasColumn('users', 'deleted_by')) {
                $table->dropColumn('deleted_by');
            }
        });
    }
};
