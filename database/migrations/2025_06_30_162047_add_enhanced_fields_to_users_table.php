<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add slug field for SEO-friendly URLs (nullable initially)
            $table->string('slug')->nullable()->unique()->after('name');

            // Add public_id field for external references (ULID) (nullable initially)
            $table->string('public_id', 26)->nullable()->unique()->after('id');

            // Add soft deletes support
            $table->softDeletes();

            // Add userstamps fields for audit trail
            $table->unsignedBigInteger('created_by')->nullable()->after('created_at');
            $table->unsignedBigInteger('updated_by')->nullable()->after('updated_at');
            $table->unsignedBigInteger('deleted_by')->nullable()->after('deleted_at');

            // Add indexes for performance
            $table->index('slug');
            $table->index('public_id');
            $table->index('created_by');
            $table->index('updated_by');
            $table->index('deleted_by');

            // Add foreign key constraints for userstamps
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('deleted_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop foreign key constraints first
            $table->dropForeign(['created_by']);
            $table->dropForeign(['updated_by']);
            $table->dropForeign(['deleted_by']);

            // Drop indexes
            $table->dropIndex(['slug']);
            $table->dropIndex(['public_id']);
            $table->dropIndex(['created_by']);
            $table->dropIndex(['updated_by']);
            $table->dropIndex(['deleted_by']);

            // Drop columns
            $table->dropColumn(['slug', 'public_id', 'deleted_at', 'created_by', 'updated_by', 'deleted_by']);
        });
    }
};
