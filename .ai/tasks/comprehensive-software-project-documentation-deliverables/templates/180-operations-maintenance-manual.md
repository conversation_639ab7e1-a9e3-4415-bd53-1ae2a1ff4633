---
owner: "[OPERATIONS_LEAD]"
last_reviewed: "[YYYY-MM-DD]"
status: "draft"
version: "1.0.0"
target_audience: "Junior developers with 6 months-2 years experience"
framework_version: "Laravel 12.x"
deployment_environment: "Production"
---

# Operations and Maintenance Manual
## [PROJECT_NAME]

**Estimated Reading Time:** 40 minutes

## Overview

This manual provides comprehensive operational procedures and maintenance guidelines for [PROJECT_NAME] running on Laravel 12.x with FilamentPHP v4. It covers system monitoring, performance optimization, security maintenance, backup procedures, and incident response.

### Operational Objectives
- **System Availability**: Maintain 99.9% uptime during business hours
- **Performance Standards**: API response times under 200ms for 95% of requests
- **Security Compliance**: Continuous GDPR compliance and security monitoring
- **Data Protection**: Automated backups with 4-hour recovery time objective
- **Incident Response**: Mean time to resolution under 2 hours for critical issues

## System Architecture Overview

### Production Environment

```mermaid
graph TB
    subgraph "Load Balancer"
        LB[Nginx Load Balancer]
    end
    
    subgraph "Application Servers"
        APP1[Laravel App Server 1]
        APP2[Laravel App Server 2]
    end
    
    subgraph "Database Layer"
        DB[SQLite Primary]
        DBB[SQLite Backup]
    end
    
    subgraph "Cache Layer"
        REDIS[Redis Cache]
    end
    
    subgraph "Storage"
        FS[File Storage]
        BACKUP[Backup Storage]
    end
    
    LB --> APP1
    LB --> APP2
    APP1 --> DB
    APP2 --> DB
    APP1 --> REDIS
    APP2 --> REDIS
    APP1 --> FS
    APP2 --> FS
    DB --> DBB
    FS --> BACKUP
```

### Key Components
- **Web Server**: Nginx with PHP-FPM
- **Application**: Laravel 12.x with FilamentPHP v4
- **Database**: SQLite with WAL mode optimization
- **Cache**: Redis for session and application caching
- **Queue**: Redis-based queue processing
- **Storage**: Local file storage with automated backup

## Daily Operations

### Morning Health Check Routine

#### System Status Verification
```bash
#!/bin/bash
# daily-health-check.sh

echo "=== Daily Health Check - $(date) ==="

# Check application status
echo "1. Application Health:"
curl -s http://localhost/health | jq '.'

# Check database connectivity
echo "2. Database Status:"
php artisan tinker --execute="DB::connection()->getPdo() ? 'Connected' : 'Failed'"

# Check cache status
echo "3. Cache Status:"
php artisan tinker --execute="Cache::store('redis')->get('health_check') ?: 'Cache OK'"

# Check queue status
echo "4. Queue Status:"
php artisan queue:monitor

# Check disk space
echo "5. Disk Usage:"
df -h | grep -E '(Filesystem|/dev/)'

# Check memory usage
echo "6. Memory Usage:"
free -h

# Check recent errors
echo "7. Recent Errors:"
tail -n 20 storage/logs/laravel.log | grep ERROR || echo "No recent errors"

echo "=== Health Check Complete ==="
```

#### Performance Monitoring
```bash
#!/bin/bash
# performance-check.sh

echo "=== Performance Monitoring - $(date) ==="

# Check response times
echo "1. API Response Times:"
for endpoint in "/api/v1/health" "/api/v1/users" "/api/v1/dashboard"
do
    time=$(curl -o /dev/null -s -w '%{time_total}' http://localhost$endpoint)
    echo "  $endpoint: ${time}s"
done

# Check database performance
echo "2. Database Performance:"
php artisan db:monitor

# Check cache hit rates
echo "3. Cache Performance:"
redis-cli info stats | grep -E "(keyspace_hits|keyspace_misses)"

# Check queue processing
echo "4. Queue Performance:"
php artisan queue:monitor --max-jobs=100

echo "=== Performance Check Complete ==="
```

### Application Monitoring

#### Laravel Telescope Monitoring
```php
<?php
// Monitor key metrics through Telescope

// Check slow queries
$slowQueries = DB::table('telescope_entries')
    ->where('type', 'query')
    ->where('content->duration', '>', 1000) // > 1 second
    ->whereDate('created_at', today())
    ->count();

// Check failed jobs
$failedJobs = DB::table('telescope_entries')
    ->where('type', 'job')
    ->where('content->status', 'failed')
    ->whereDate('created_at', today())
    ->count();

// Check exception rates
$exceptions = DB::table('telescope_entries')
    ->where('type', 'exception')
    ->whereDate('created_at', today())
    ->count();
```

#### Custom Health Checks
```php
<?php
// app/Http/Controllers/HealthController.php

class HealthController extends Controller
{
    public function check()
    {
        $checks = [
            'database' => $this->checkDatabase(),
            'cache' => $this->checkCache(),
            'storage' => $this->checkStorage(),
            'queue' => $this->checkQueue(),
            'memory' => $this->checkMemory(),
        ];
        
        $healthy = collect($checks)->every(fn($check) => $check['status'] === 'ok');
        
        return response()->json([
            'status' => $healthy ? 'healthy' : 'unhealthy',
            'timestamp' => now()->toISOString(),
            'checks' => $checks,
        ], $healthy ? 200 : 503);
    }
    
    private function checkDatabase(): array
    {
        try {
            DB::connection()->getPdo();
            $responseTime = $this->measureTime(fn() => DB::select('SELECT 1'));
            
            return [
                'status' => 'ok',
                'response_time' => $responseTime . 'ms',
            ];
        } catch (Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage(),
            ];
        }
    }
    
    private function checkCache(): array
    {
        try {
            $key = 'health_check_' . time();
            Cache::put($key, 'test', 60);
            $value = Cache::get($key);
            Cache::forget($key);
            
            return [
                'status' => $value === 'test' ? 'ok' : 'error',
                'message' => $value === 'test' ? 'Cache working' : 'Cache failed',
            ];
        } catch (Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage(),
            ];
        }
    }
}
```

## Performance Optimization

### Database Optimization

#### SQLite Performance Tuning
```php
<?php
// app/Providers/SqliteServiceProvider.php

class SqliteServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        if (config('database.default') === 'sqlite') {
            // Optimize SQLite for production
            DB::statement('PRAGMA journal_mode=WAL');
            DB::statement('PRAGMA synchronous=NORMAL');
            DB::statement('PRAGMA cache_size=10000');
            DB::statement('PRAGMA temp_store=MEMORY');
            DB::statement('PRAGMA mmap_size=268435456'); // 256MB
            DB::statement('PRAGMA optimize');
        }
    }
}
```

#### Query Optimization Monitoring
```bash
#!/bin/bash
# query-optimization.sh

echo "=== Query Optimization Report - $(date) ==="

# Analyze slow queries
php artisan tinker --execute="
\$slowQueries = DB::table('telescope_entries')
    ->where('type', 'query')
    ->where('content->duration', '>', 500)
    ->whereDate('created_at', today())
    ->orderBy('content->duration', 'desc')
    ->limit(10)
    ->get(['content']);

foreach (\$slowQueries as \$query) {
    \$content = json_decode(\$query->content);
    echo 'Duration: ' . \$content->duration . 'ms - SQL: ' . \$content->sql . PHP_EOL;
}
"

# Check for N+1 queries
echo "Checking for potential N+1 queries..."
php artisan telescope:prune --hours=1
```

### Cache Management

#### Cache Warming Strategy
```php
<?php
// app/Console/Commands/WarmCache.php

class WarmCache extends Command
{
    protected $signature = 'cache:warm';
    protected $description = 'Warm application cache with frequently accessed data';

    public function handle(): int
    {
        $this->info('Warming application cache...');
        
        // Warm user permissions cache
        $this->warmUserPermissions();
        
        // Warm configuration cache
        $this->warmConfiguration();
        
        // Warm route cache
        $this->warmRoutes();
        
        $this->info('Cache warming completed successfully!');
        
        return Command::SUCCESS;
    }
    
    private function warmUserPermissions(): void
    {
        User::with(['roles.permissions'])->chunk(100, function ($users) {
            foreach ($users as $user) {
                Cache::remember(
                    "user.permissions.{$user->id}",
                    3600,
                    fn() => $user->getAllPermissions()
                );
            }
        });
        
        $this->info('User permissions cache warmed');
    }
    
    private function warmConfiguration(): void
    {
        $configs = ['app', 'database', 'cache', 'queue', 'mail'];
        
        foreach ($configs as $config) {
            Cache::remember(
                "config.{$config}",
                3600,
                fn() => config($config)
            );
        }
        
        $this->info('Configuration cache warmed');
    }
}
```

#### Cache Invalidation Strategy
```php
<?php
// app/Observers/UserObserver.php

class UserObserver
{
    public function updated(User $user): void
    {
        // Invalidate user-specific caches
        Cache::forget("user.permissions.{$user->id}");
        Cache::forget("user.profile.{$user->id}");
        
        // Invalidate related caches
        if ($user->wasChanged(['name', 'email'])) {
            Cache::tags(['users', 'profiles'])->flush();
        }
    }
    
    public function deleted(User $user): void
    {
        // Clean up all user-related cache entries
        Cache::forget("user.permissions.{$user->id}");
        Cache::forget("user.profile.{$user->id}");
        Cache::tags(['users'])->flush();
    }
}
```

## Security Maintenance

### Security Monitoring

#### Daily Security Checks
```bash
#!/bin/bash
# security-check.sh

echo "=== Security Check - $(date) ==="

# Check for failed login attempts
echo "1. Failed Login Attempts (last 24h):"
php artisan tinker --execute="
\$attempts = DB::table('activity_log')
    ->where('description', 'login_failed')
    ->where('created_at', '>=', now()->subDay())
    ->count();
echo 'Failed attempts: ' . \$attempts;
"

# Check for suspicious activity
echo "2. Suspicious Activity:"
php artisan tinker --execute="
\$suspicious = DB::table('activity_log')
    ->where('description', 'LIKE', '%suspicious%')
    ->where('created_at', '>=', now()->subDay())
    ->count();
echo 'Suspicious activities: ' . \$suspicious;
"

# Check SSL certificate expiry
echo "3. SSL Certificate Status:"
openssl s_client -connect localhost:443 -servername yourdomain.com 2>/dev/null | openssl x509 -noout -dates

# Check for security updates
echo "4. Security Updates:"
composer audit

echo "=== Security Check Complete ==="
```

#### GDPR Compliance Monitoring
```php
<?php
// app/Console/Commands/GdprComplianceCheck.php

class GdprComplianceCheck extends Command
{
    protected $signature = 'gdpr:compliance-check';
    protected $description = 'Check GDPR compliance status';

    public function handle(): int
    {
        $this->info('Running GDPR compliance check...');
        
        // Check data retention compliance
        $this->checkDataRetention();
        
        // Check consent management
        $this->checkConsentManagement();
        
        // Check data subject requests
        $this->checkDataSubjectRequests();
        
        return Command::SUCCESS;
    }
    
    private function checkDataRetention(): void
    {
        $retentionPeriod = config('gdpr.retention_period', 2); // years
        $cutoffDate = now()->subYears($retentionPeriod);
        
        $expiredUsers = User::onlyTrashed()
            ->where('deleted_at', '<', $cutoffDate)
            ->whereNull('anonymized_at')
            ->count();
        
        if ($expiredUsers > 0) {
            $this->warn("Found {$expiredUsers} users requiring anonymization");
        } else {
            $this->info('Data retention compliance: OK');
        }
    }
    
    private function checkConsentManagement(): void
    {
        $usersWithoutConsent = User::whereDoesntHave('consents')->count();
        
        if ($usersWithoutConsent > 0) {
            $this->warn("Found {$usersWithoutConsent} users without consent records");
        } else {
            $this->info('Consent management compliance: OK');
        }
    }
}
```

### Vulnerability Management

#### Automated Security Scanning
```bash
#!/bin/bash
# security-scan.sh

echo "=== Security Scan - $(date) ==="

# Check for known vulnerabilities in dependencies
echo "1. Dependency Vulnerability Scan:"
composer audit --format=json > security-audit.json

# Check for outdated packages
echo "2. Outdated Packages:"
composer outdated --direct

# Run static analysis for security issues
echo "3. Static Security Analysis:"
./vendor/bin/phpstan analyse --level=8 app/

# Check file permissions
echo "4. File Permissions Check:"
find storage/ -type f -not -perm 644 -ls
find bootstrap/cache/ -type f -not -perm 644 -ls

echo "=== Security Scan Complete ==="
```

## Backup and Recovery

### Automated Backup Strategy

#### Database Backup
```bash
#!/bin/bash
# backup-database.sh

BACKUP_DIR="/var/backups/laravel"
DATE=$(date +%Y%m%d_%H%M%S)
DB_PATH="/path/to/database.sqlite"

echo "=== Database Backup - $(date) ==="

# Create backup directory
mkdir -p $BACKUP_DIR

# Create database backup
cp $DB_PATH "$BACKUP_DIR/database_$DATE.sqlite"

# Compress backup
gzip "$BACKUP_DIR/database_$DATE.sqlite"

# Verify backup integrity
if [ -f "$BACKUP_DIR/database_$DATE.sqlite.gz" ]; then
    echo "Database backup created successfully: database_$DATE.sqlite.gz"
else
    echo "ERROR: Database backup failed!"
    exit 1
fi

# Clean up old backups (keep last 30 days)
find $BACKUP_DIR -name "database_*.sqlite.gz" -mtime +30 -delete

echo "=== Database Backup Complete ==="
```

#### Application Backup
```bash
#!/bin/bash
# backup-application.sh

BACKUP_DIR="/var/backups/laravel"
DATE=$(date +%Y%m%d_%H%M%S)
APP_DIR="/var/www/laravel"

echo "=== Application Backup - $(date) ==="

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup storage directory
tar -czf "$BACKUP_DIR/storage_$DATE.tar.gz" -C $APP_DIR storage/

# Backup .env file
cp "$APP_DIR/.env" "$BACKUP_DIR/env_$DATE"

# Backup uploaded files
if [ -d "$APP_DIR/public/uploads" ]; then
    tar -czf "$BACKUP_DIR/uploads_$DATE.tar.gz" -C $APP_DIR public/uploads/
fi

echo "=== Application Backup Complete ==="
```

### Recovery Procedures

#### Database Recovery
```bash
#!/bin/bash
# restore-database.sh

BACKUP_FILE=$1
DB_PATH="/path/to/database.sqlite"

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup_file>"
    exit 1
fi

echo "=== Database Recovery - $(date) ==="

# Stop application
sudo systemctl stop php8.1-fpm
sudo systemctl stop nginx

# Backup current database
cp $DB_PATH "${DB_PATH}.backup.$(date +%Y%m%d_%H%M%S)"

# Restore from backup
if [[ $BACKUP_FILE == *.gz ]]; then
    gunzip -c $BACKUP_FILE > $DB_PATH
else
    cp $BACKUP_FILE $DB_PATH
fi

# Set proper permissions
chown www-data:www-data $DB_PATH
chmod 644 $DB_PATH

# Start application
sudo systemctl start php8.1-fpm
sudo systemctl start nginx

# Verify recovery
php artisan tinker --execute="DB::connection()->getPdo() ? 'Database OK' : 'Database Failed'"

echo "=== Database Recovery Complete ==="
```

## Incident Response

### Incident Classification

| Severity | Description | Response Time | Examples |
|----------|-------------|---------------|----------|
| **Critical** | System down, data loss | 15 minutes | Database corruption, security breach |
| **High** | Major functionality impaired | 1 hour | Authentication failure, payment processing down |
| **Medium** | Minor functionality affected | 4 hours | Non-critical feature broken, performance degradation |
| **Low** | Cosmetic issues, minor bugs | 24 hours | UI glitches, documentation errors |

### Incident Response Procedures

#### Critical Incident Response
```bash
#!/bin/bash
# critical-incident-response.sh

echo "=== CRITICAL INCIDENT RESPONSE - $(date) ==="

# 1. Immediate assessment
echo "1. System Status Check:"
curl -s http://localhost/health || echo "SYSTEM DOWN"

# 2. Enable maintenance mode
echo "2. Enabling maintenance mode:"
php artisan down --message="System maintenance in progress"

# 3. Capture system state
echo "3. Capturing system state:"
ps aux > /tmp/processes_$(date +%Y%m%d_%H%M%S).log
df -h > /tmp/disk_usage_$(date +%Y%m%d_%H%M%S).log
free -h > /tmp/memory_usage_$(date +%Y%m%d_%H%M%S).log

# 4. Check recent logs
echo "4. Recent error logs:"
tail -n 100 storage/logs/laravel.log

# 5. Notify stakeholders
echo "5. Sending incident notification:"
# Add notification logic here

echo "=== CRITICAL INCIDENT RESPONSE INITIATED ==="
```

#### Recovery Verification
```bash
#!/bin/bash
# verify-recovery.sh

echo "=== Recovery Verification - $(date) ==="

# Test database connectivity
echo "1. Database Test:"
php artisan tinker --execute="DB::connection()->getPdo() ? 'OK' : 'FAILED'"

# Test cache functionality
echo "2. Cache Test:"
php artisan tinker --execute="Cache::put('test', 'value', 60); Cache::get('test') === 'value' ? 'OK' : 'FAILED'"

# Test queue processing
echo "3. Queue Test:"
php artisan queue:work --once --timeout=30

# Test API endpoints
echo "4. API Test:"
curl -s http://localhost/api/v1/health | jq '.status'

# Test admin panel
echo "5. Admin Panel Test:"
curl -s http://localhost/admin/login | grep -q "Login" && echo "OK" || echo "FAILED"

# Disable maintenance mode if all tests pass
echo "6. Disabling maintenance mode:"
php artisan up

echo "=== Recovery Verification Complete ==="
```

## Maintenance Schedules

### Daily Maintenance Tasks
- [ ] **Health Check**: Run automated health check script
- [ ] **Log Review**: Review error logs and security logs
- [ ] **Backup Verification**: Verify automated backups completed successfully
- [ ] **Performance Check**: Monitor response times and resource usage
- [ ] **Security Scan**: Check for failed login attempts and suspicious activity

### Weekly Maintenance Tasks
- [ ] **Dependency Updates**: Check for security updates and patches
- [ ] **Performance Analysis**: Analyze slow queries and optimize database
- [ ] **Cache Optimization**: Review cache hit rates and optimize strategies
- [ ] **GDPR Compliance**: Run compliance checks and data retention cleanup
- [ ] **Capacity Planning**: Review resource usage trends and plan scaling

### Monthly Maintenance Tasks
- [ ] **Security Audit**: Comprehensive security review and penetration testing
- [ ] **Backup Testing**: Test backup restoration procedures
- [ ] **Documentation Review**: Update operational documentation
- [ ] **Performance Tuning**: Optimize application and database performance
- [ ] **Disaster Recovery Test**: Test disaster recovery procedures

---

**Operations Manual Version**: 1.0.0  
**Environment**: Production Laravel 12.x with FilamentPHP v4  
**Created**: [YYYY-MM-DD]  
**Last Updated**: [YYYY-MM-DD]  
**Next Review**: [YYYY-MM-DD]  
**Operations Owner**: [OPERATIONS_LEAD]
